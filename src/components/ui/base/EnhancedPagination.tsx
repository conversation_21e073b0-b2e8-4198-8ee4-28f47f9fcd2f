"use client";

import { ChevronLeft, ChevronRight, ChevronDown } from "lucide-react";
import PageSizeSelector from "./PageSizeSelector";

interface EnhancedPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  isLoading?: boolean;
  className?: string;
  showPageSizeSelector?: boolean;
  pageSizeOptions?: number[];
}

export default function EnhancedPagination({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
  className = "",
  showPageSizeSelector = true,
  pageSizeOptions = [10, 20, 50, 100]
}: EnhancedPaginationProps) {


  // Generate page numbers to show
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first page
      pages.push(1);
      
      if (currentPage > 3) {
        pages.push('...');
      }
      
      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      if (currentPage < totalPages - 2) {
        pages.push('...');
      }
      
      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const pageNumbers = getPageNumbers();

  const handlePrevious = () => {
    if (currentPage > 1 && !isLoading) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages && !isLoading) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (page: number) => {
    if (page !== currentPage && !isLoading) {
      onPageChange(page);
    }
  };

  if (totalItems === 0) {
    return (
      <div className={`flex items-center justify-end ${className}`}>
        <div className="text-sm text-gray-500">
          Tidak ada data untuk ditampilkan
        </div>
        {showPageSizeSelector && (
          <PageSizeSelector
            currentPageSize={pageSize}
            onPageSizeChange={onPageSizeChange}
            options={pageSizeOptions}
            totalItems={0}
          />
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-end gap-4 ${className}`}>
      {/* Left side: Info text and page size selector */}
      <div className="flex items-center gap-4">
        {/* Current range info */}
        <div className="text-sm text-gray-600">
          {pageSize} dari {totalItems} total data
        </div>

        {/* Page size selector */}
        {showPageSizeSelector && (
          <div className="relative">
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              disabled={isLoading}
              className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {pageSizeOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        )}
      </div>

      {/* Right side: Pagination controls */}
      <div className="flex items-center gap-1">
        {/* Previous button */}
        <button
          onClick={handlePrevious}
          disabled={currentPage === 1 || isLoading}
          className="flex items-center justify-center w-10 h-10 border border-gray-300 rounded-md bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        {/* Page numbers */}
        {pageNumbers.map((page, index) => (
          <div key={index}>
            {page === '...' ? (
              <span className="flex items-center justify-center w-10 h-10 text-sm text-gray-500">...</span>
            ) : (
              <button
                onClick={() => handlePageClick(page as number)}
                disabled={isLoading}
                className={`flex items-center justify-center w-10 h-10 text-sm border rounded-md transition-colors ${
                  page === currentPage
                    ? 'bg-primary-default text-white border-default-primary'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {page}
              </button>
            )}
          </div>
        ))}

        {/* Next button */}
        <button
          onClick={handleNext}
          disabled={currentPage === totalPages || isLoading}
          className="flex items-center justify-center w-10 h-10 border border-gray-300 rounded-md bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

// Compact version for smaller spaces
export function CompactPagination({
  currentPage,
  totalPages,
  onPageChange,
  isLoading = false,
  className = ""
}: Pick<EnhancedPaginationProps, 'currentPage' | 'totalPages' | 'onPageChange' | 'isLoading' | 'className'>) {
  
  const handlePrevious = () => {
    if (currentPage > 1 && !isLoading) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages && !isLoading) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <button
        onClick={handlePrevious}
        disabled={currentPage === 1 || isLoading}
        className="p-1 border border-gray-300 rounded bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ChevronLeft className="w-4 h-4" />
      </button>
      
      <span className="px-3 py-1 text-sm bg-gray-100 rounded">
        {currentPage} / {totalPages}
      </span>
      
      <button
        onClick={handleNext}
        disabled={currentPage === totalPages || isLoading}
        className="p-1 border border-gray-300 rounded bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ChevronRight className="w-4 h-4" />
      </button>
    </div>
  );
}
