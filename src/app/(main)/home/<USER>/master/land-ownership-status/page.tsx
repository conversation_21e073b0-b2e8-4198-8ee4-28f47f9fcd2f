"use client";
import DistributionMethodModal from "@/components/ui/home/<USER>/master/modal/DistributionMethodModal";
import Search from "@/components/ui/search";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  EllipsisVertical,
  Plus,
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import OwnershipStatusModal from "@/components/ui/home/<USER>/master/modal/OwnershipStatusModal";
import { useAuth } from "@/hooks/useAuth";
import { StatusKepemilikan } from "@/types/master/statusKepemilikan";
import { deletestatusKepemilikanData, fetchStatusKepemilikanData, poststatusKepemilikanData, putstatusKepemilikanData, searchstatusKepemilikanData } from "@/lib/master/statusKepemilikanFetching";
import { Bounce, toast } from "react-toastify";
import ConfirmasiDeleteModal from "@/components/ui/home/<USER>/master/modal/ConfirmasiDeleteModal";
import { usePageSize } from "@/components/ui/base/PageSizeSelector";
import EnhancedPagination from "@/components/ui/base/EnhancedPagination";

/**
 * The `LandOwnershipStatusPage` component renders a page that displays a list of distribution methods
 * with functionalities to search, add, edit, and delete distribution methods.
 *
 * @component
 * @example
 * return (
 *   <LandOwnershipStatusPage />
 * )
 *
 * @returns {JSX.Element} The rendered component.
 *
 * @remarks
 * This component uses several custom UI components such as `Search`, `Table`, `DropdownMenu`, and `DistributionMethodModal`.
 * It also utilizes the `useRouter` hook from Next.js for navigation and `useState` hook from React for state management.
 *
 * @function
 * @name LandOwnershipStatusPage
 *
 * @description
 * The component maintains the following states:
 * - `search`: A string representing the search query.
 * - `isModalOpen`: A boolean indicating whether the modal is open or not.
 * - `status`: A string representing the current status of the modal (e.g., "Tambah", "Detail", "Edit").
 * - `listStatus`: An array of objects representing the list of distribution methods.
 *
 * The component provides the following functionalities:
 * - `handleChange`: Updates the `search` state with the new value.
 * - `handleOpenModal`: Opens the modal and sets the `status` state with the provided slug.
 *
 * The component renders a search input, a button to add new distribution methods, a table displaying the list of distribution methods,
 * and a modal for adding, viewing details, and editing distribution methods.
 */
export default function LandOwnershipStatusPage() {
  const { getToken } = useAuth();
  const token = getToken();
  const { pageSize } = usePageSize();
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [status, setStatus] = useState("");

  const [listStatus, setlistStatus] = useState<StatusKepemilikan[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
  });
  const [messageError, setMessageError] = useState<Record<keyof typeof formData, string | null>>({
    name: null
  });
  const clearMessageError = () => {
    setMessageError({
      name: null
    });
  };
  const clearFormData = () => {
    setFormData({
      name: ""
    });
  };
  const [isLoading, setIsLoading] = useState(false);
  const [id, setId] = useState<number | null>(null);

  const handlePageSizeChange = (newPageSize: number) => {
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const handleChange = (value: string) => {
    setSearch(value);
    if (value.length > 0) {
      handleSearchStatusKepemilikan(value);
    } else {
      fetchPage(1);
    }
  };

  const handleOpenModal = (slug: string, id?: number) => {
    setIsModalOpen(true);
    setId(id ?? 0);
    setFormData({
      name: id ? listStatus.find((item) => item.id === id)?.name ?? "" : "",
    });
    setStatus(slug);
  };

  const fetchPage = useCallback(async (page: number) => {
    if (loading) return;

    setLoading(true);
    try {
      const data = await fetchStatusKepemilikanData(page, String(token));
      setlistStatus(data.items || []);
      setTotalPages(data.total_pages || 0);
      setTotalItems(data.total_items || 0);
    } catch (error) {
      console.error("Error fetching ownership status:", error);
      setlistStatus([]);
      setTotalPages(0);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  }, [loading, token]);

  const handleSearchStatusKepemilikan = async (search: string) => {
    setLoading(true);
    try {
      const data = await searchstatusKepemilikanData(search, String(token));
      setlistStatus(data.items || []);
      setTotalPages(data.total_pages || 0);
      setTotalItems(data.total_items || 0);
    } catch (error) {
      console.error("Error searching ownership status:", error);
      setlistStatus([]);
      setTotalPages(0);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    clearMessageError();
    if (status === "Tambah") {
      await poststatusKepemilikanData(formData.name, String(token))
        .then((response) => {
          if (!response.ok) {
            response.json().then((errorData) => {
              setMessageError(errorData.data);
            });
            throw new Error('Failed to save data');
          }
          return response.json();
        })
        .then((data) => {
          toast.success('Data berhasil disimpan', {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
            transition: Bounce,
          });
          setIsModalOpen(false);
          fetchPage(1);
          clearFormData();
          setIsLoading(false);
        })
        .catch((error) => {
          setIsLoading(false);
          console.error('Error:', error);
          toast.error(`${error}`, {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
            transition: Bounce,
          });
        });
    } else if (status === "Edit") {
      await putstatusKepemilikanData(Number(id), formData.name, String(token))
        .then((response) => {
          if (!response.ok) {
            response.json().then((errorData) => {
              setMessageError(errorData.data);
            });
            throw new Error('Failed to save data');
          }
          return response.json();
        })
        .then((data) => {
          toast.success('Data berhasil diupdate', {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
            transition: Bounce,
          });
          setIsModalOpen(false);
          fetchPage(1);
          clearFormData();
          setIsLoading(false);
        })
        .catch((error) => {
          setIsLoading(false);
          console.error('Error:', error);
          toast.error(`${error}`, {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
            transition: Bounce,
          });
        });
    }
  }

  const handleDeleteModal = (id: number) => {
    setIsOpen(true);
    setId(id);
  };

  const handleDelete = async () => {
    setIsLoading(true);
    await deletestatusKepemilikanData(Number(id), String(token))
      .then((response) => {
        if (!response.ok) {
          response.json().then((errorData) => {
            setMessageError(errorData.data);
          });
          throw new Error('Failed to delete data');
        }
        return response.json();
      })
      .then((data) => {
        setIsOpen(false);
        toast.success('Data berhasil dihapus', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
        setIsModalOpen(false);
        fetchPage(1);
        clearFormData();
        setIsLoading(false);
      })
      .catch((error) => {
        setIsLoading(false);
        console.error('Error:', error);
        toast.error(`${error}`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      })
  }

  useEffect(() => {
    fetchPage(currentPage);
  }, [currentPage, pageSize]);

  return (
    <div className="bg-white p-4 rounded-md shadow-md font-poppins">
      <div className="text-lg font-medium mb-4">Status Kepemilikan Lahaan</div>
      <div className="flex justify-between items-center gap-4 mb-4">
        <div className="w-full">
          <Search value={search} onChange={handleChange} />
        </div>
        <div>
          <button
            onClick={() => handleOpenModal("Tambah")}
            className="bg-primary-500 flex text-white px-5 py-2 text-nowrap rounded-full"
          >
            <Plus className="mr-2" />
            Tambah
          </button>
        </div>
      </div>
      <Table className="mt-4 overflow-hidden">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] bg-gray-200 text-center">
              No
            </TableHead>
            <TableHead className="bg-gray-200 text-center">
              Status Kepemilikan Lahan
            </TableHead>
            <TableHead className="text-right bg-gray-200"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell className="w-[50px] text-center">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="font-medium text-center">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
            ))
          ) : listStatus.length === 0 ? (
            <TableRow>
              <TableCell colSpan={3} className="text-center">
                Tidak ada data yang tersedia.
              </TableCell>
            </TableRow>
          ) : (
            listStatus.map((value) => (
              <TableRow key={listStatus.indexOf(value)}>
                <TableCell className="w-[50px] text-center">
                  {listStatus.indexOf(value) + 1}
                </TableCell>
                <TableCell className="font-medium text-center">
                  {value.name}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger className="border-none bg-transparent active:border-none focus:border-none">
                        <EllipsisVertical className="cursor-pointer" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => handleOpenModal("Detail", value.id)}
                        >
                          Detail
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => handleOpenModal("Edit", value.id)}
                        >
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => handleDeleteModal(value.id)}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={3}>
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={handlePageSizeChange}
                isLoading={loading}
                className="mt-4"
              />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>

      {/* Component */}
      <OwnershipStatusModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        onChange={(value) => {
          setFormData({ ...formData, name: value });
          clearMessageError();
        }
        }
        onSubmit={handleSubmit}
        value={formData.name}
        errorMessage={messageError.name ?? ''}
        isLoading={isLoading}
        status={status}
      />
      <ConfirmasiDeleteModal isOpen={isOpen} onBatal={() => { setIsOpen(false) }} onClose={() => { setIsOpen(false); }} onSimpan={handleDelete} status={'Status Kepemilikan Lahan'} />
    </div>
  );
}
